#!/usr/bin/env python3
"""
Real-time Paper Trading System for Options Manipulation Detection
Simulates trades based on detected manipulation signals and tracks P&L
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

from models.data_models import ManipulationSignal, OptionsData, PatternType
from utils.database import db_manager

logger = logging.getLogger(__name__)

class TradeAction(str, Enum):
    """Trade actions based on manipulation signals"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

class TradeStatus(str, Enum):
    """Trade execution status"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    EXPIRED = "EXPIRED"

@dataclass
class PaperTrade:
    """Represents a paper trade based on manipulation signal"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    signal_id: str = ""
    symbol: str = ""
    strike: float = 0.0
    option_type: str = ""
    action: TradeAction = TradeAction.HOLD
    entry_price: float = 0.0
    exit_price: float = 0.0
    quantity: int = 0
    entry_time: datetime = field(default_factory=datetime.now)
    exit_time: Optional[datetime] = None
    status: TradeStatus = TradeStatus.OPEN
    profit_loss: float = 0.0
    profit_loss_percent: float = 0.0
    confidence: float = 0.0
    manipulation_type: str = ""
    estimated_profit: float = 0.0
    actual_profit: float = 0.0
    trade_reason: str = ""
    market_data: Dict[str, Any] = field(default_factory=dict)

class PaperTradingEngine:
    """
    Real-time paper trading engine that executes trades based on manipulation signals
    """
    
    def __init__(self, initial_capital: float = 1000000.0):  # ₹10 lakh starting capital
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.available_capital = initial_capital
        self.open_trades: Dict[str, PaperTrade] = {}
        self.closed_trades: List[PaperTrade] = []
        self.total_profit_loss = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_position_size = 0.1  # Max 10% of capital per trade
        self.stop_loss_percent = 0.15  # 15% stop loss
        self.take_profit_percent = 0.25  # 25% take profit
        
    async def process_manipulation_signal(self, signal: ManipulationSignal, current_market_data: List[OptionsData]) -> Optional[PaperTrade]:
        """
        Process a manipulation signal and decide whether to execute a paper trade
        
        Args:
            signal: Detected manipulation signal
            current_market_data: Current market data for pricing
            
        Returns:
            PaperTrade if trade was executed, None otherwise
        """
        try:
            # Analyze signal for trading opportunity
            trade_decision = self._analyze_signal_for_trade(signal)
            
            if trade_decision["should_trade"]:
                # Find relevant market data for the signal
                relevant_option = self._find_relevant_option(signal, current_market_data)
                
                if relevant_option:
                    # Execute paper trade
                    trade = await self._execute_paper_trade(signal, relevant_option, trade_decision)
                    
                    if trade:
                        logger.info(f"📈 PAPER TRADE EXECUTED: {trade.action} {trade.symbol} at ₹{trade.entry_price}")
                        return trade
                        
        except Exception as e:
            logger.error(f"Error processing manipulation signal for trading: {str(e)}")
            
        return None
    
    def _analyze_signal_for_trade(self, signal: ManipulationSignal) -> Dict[str, Any]:
        """
        Analyze manipulation signal to determine trading strategy
        
        Args:
            signal: Manipulation signal to analyze
            
        Returns:
            Dictionary with trading decision and strategy
        """
        decision = {
            "should_trade": False,
            "action": TradeAction.HOLD,
            "confidence_threshold": 0.8,
            "position_size_multiplier": 1.0,
            "reason": ""
        }
        
        # Only trade on high confidence signals
        if signal.confidence < decision["confidence_threshold"]:
            decision["reason"] = f"Low confidence: {signal.confidence:.1%}"
            return decision
        
        # Determine trade action based on manipulation type
        if signal.pattern_type == PatternType.ORDER_SPOOFING:
            # For spoofing, we expect price to revert after manipulation
            if "bid_spoofing" in signal.description.lower():
                # Bid spoofing artificially inflates price, expect it to fall
                decision["action"] = TradeAction.SELL
                decision["reason"] = "Bid spoofing detected - expect price reversion"
            elif "ask_spoofing" in signal.description.lower():
                # Ask spoofing artificially deflates price, expect it to rise
                decision["action"] = TradeAction.BUY
                decision["reason"] = "Ask spoofing detected - expect price recovery"
            else:
                decision["action"] = TradeAction.BUY  # Default for general spoofing
                decision["reason"] = "General spoofing detected"
                
            decision["should_trade"] = True
            
        # Adjust position size based on confidence
        if signal.confidence >= 0.9:
            decision["position_size_multiplier"] = 1.5  # Larger position for very high confidence
        elif signal.confidence >= 0.85:
            decision["position_size_multiplier"] = 1.2
        else:
            decision["position_size_multiplier"] = 0.8  # Smaller position for lower confidence
            
        return decision
    
    def _find_relevant_option(self, signal: ManipulationSignal, market_data: List[OptionsData]) -> Optional[OptionsData]:
        """
        Find the relevant option from market data based on the signal
        
        Args:
            signal: Manipulation signal
            market_data: Current market data
            
        Returns:
            Relevant OptionsData or None
        """
        if not signal.symbols_affected:
            return None
            
        # Parse symbol from signal (format: "NIFTY_25000.0_CE")
        try:
            symbol_parts = signal.symbols_affected[0].split('_')
            if len(symbol_parts) >= 3:
                symbol = symbol_parts[0]
                strike = float(symbol_parts[1])
                option_type = symbol_parts[2]
                
                # Find matching option in market data
                for option in market_data:
                    if (option.symbol == symbol and 
                        option.strike == strike and 
                        option.option_type.value == option_type):
                        return option
                        
        except Exception as e:
            logger.error(f"Error parsing signal symbol: {str(e)}")
            
        return None
    
    async def _execute_paper_trade(self, signal: ManipulationSignal, option: OptionsData, decision: Dict[str, Any]) -> Optional[PaperTrade]:
        """
        Execute a paper trade based on the signal and decision
        
        Args:
            signal: Manipulation signal
            option: Option to trade
            decision: Trading decision
            
        Returns:
            PaperTrade if executed successfully
        """
        try:
            # Calculate position size
            position_value = self.current_capital * self.max_position_size * decision["position_size_multiplier"]
            quantity = int(position_value / (option.last_price * 50))  # 50 is lot size
            
            if quantity <= 0:
                logger.warning("Calculated quantity is 0, skipping trade")
                return None
                
            # Check if we have enough capital
            trade_value = quantity * option.last_price * 50
            if trade_value > self.available_capital:
                logger.warning(f"Insufficient capital for trade: need ₹{trade_value:,.0f}, have ₹{self.available_capital:,.0f}")
                return None
            
            # Create paper trade
            trade = PaperTrade(
                signal_id=signal.id,
                symbol=f"{option.symbol}_{option.strike}_{option.option_type.value}",
                strike=option.strike,
                option_type=option.option_type.value,
                action=decision["action"],
                entry_price=option.last_price,
                quantity=quantity,
                entry_time=datetime.now(),
                confidence=signal.confidence,
                manipulation_type=signal.pattern_type.value,
                estimated_profit=signal.estimated_profit,
                trade_reason=decision["reason"],
                market_data={
                    "bid_price": option.bid_price,
                    "ask_price": option.ask_price,
                    "volume": option.volume,
                    "open_interest": option.open_interest
                }
            )
            
            # Update capital allocation
            self.available_capital -= trade_value
            self.open_trades[trade.id] = trade
            self.total_trades += 1
            
            # Store trade in database
            await self._store_trade_in_database(trade)
            
            logger.info(f"✅ Paper trade executed: {trade.action} {quantity} lots of {trade.symbol} at ₹{trade.entry_price}")
            
            return trade
            
        except Exception as e:
            logger.error(f"Error executing paper trade: {str(e)}")
            return None
    
    async def update_open_trades(self, current_market_data: List[OptionsData]):
        """
        Update open trades with current market prices and check for exit conditions
        
        Args:
            current_market_data: Current market data for pricing
        """
        trades_to_close = []
        
        for trade_id, trade in self.open_trades.items():
            try:
                # Find current price for this option
                current_option = self._find_option_by_symbol(trade.symbol, current_market_data)
                
                if current_option:
                    current_price = current_option.last_price
                    
                    # Calculate current P&L
                    if trade.action == TradeAction.BUY:
                        pnl = (current_price - trade.entry_price) * trade.quantity * 50
                        pnl_percent = (current_price - trade.entry_price) / trade.entry_price
                    else:  # SELL
                        pnl = (trade.entry_price - current_price) * trade.quantity * 50
                        pnl_percent = (trade.entry_price - current_price) / trade.entry_price
                    
                    trade.profit_loss = pnl
                    trade.profit_loss_percent = pnl_percent
                    
                    # Check exit conditions
                    should_close = False
                    close_reason = ""
                    
                    # Stop loss check
                    if pnl_percent <= -self.stop_loss_percent:
                        should_close = True
                        close_reason = f"Stop loss hit: {pnl_percent:.1%}"
                    
                    # Take profit check
                    elif pnl_percent >= self.take_profit_percent:
                        should_close = True
                        close_reason = f"Take profit hit: {pnl_percent:.1%}"
                    
                    # Time-based exit (close after 1 hour)
                    elif datetime.now() - trade.entry_time > timedelta(hours=1):
                        should_close = True
                        close_reason = "Time-based exit (1 hour)"
                    
                    if should_close:
                        trade.exit_price = current_price
                        trade.exit_time = datetime.now()
                        trade.status = TradeStatus.CLOSED
                        trade.actual_profit = pnl
                        trades_to_close.append(trade_id)
                        
                        logger.info(f"🔄 Closing trade: {trade.symbol} - {close_reason} - P&L: ₹{pnl:,.0f}")
                        
            except Exception as e:
                logger.error(f"Error updating trade {trade_id}: {str(e)}")
        
        # Close trades that met exit conditions
        for trade_id in trades_to_close:
            await self._close_trade(trade_id)
    
    def _find_option_by_symbol(self, symbol: str, market_data: List[OptionsData]) -> Optional[OptionsData]:
        """Find option in market data by symbol string"""
        try:
            parts = symbol.split('_')
            if len(parts) >= 3:
                sym = parts[0]
                strike = float(parts[1])
                opt_type = parts[2]
                
                for option in market_data:
                    if (option.symbol == sym and 
                        option.strike == strike and 
                        option.option_type.value == opt_type):
                        return option
        except:
            pass
        return None
    
    async def _close_trade(self, trade_id: str):
        """Close a trade and update statistics"""
        if trade_id in self.open_trades:
            trade = self.open_trades[trade_id]
            
            # Update capital
            trade_value = trade.quantity * trade.exit_price * 50
            self.available_capital += trade_value
            self.current_capital += trade.actual_profit
            self.total_profit_loss += trade.actual_profit
            
            # Update statistics
            if trade.actual_profit > 0:
                self.winning_trades += 1
            else:
                self.losing_trades += 1
            
            # Move to closed trades
            self.closed_trades.append(trade)
            del self.open_trades[trade_id]
            
            # Update database
            await self._update_trade_in_database(trade)
    
    async def _store_trade_in_database(self, trade: PaperTrade):
        """Store paper trade in database"""
        try:
            # Implementation would store trade in database
            # For now, just log it
            logger.info(f"📊 Storing trade in database: {trade.id}")
        except Exception as e:
            logger.error(f"Error storing trade in database: {str(e)}")
    
    async def _update_trade_in_database(self, trade: PaperTrade):
        """Update paper trade in database"""
        try:
            # Implementation would update trade in database
            logger.info(f"📊 Updating trade in database: {trade.id} - P&L: ₹{trade.actual_profit:,.0f}")
        except Exception as e:
            logger.error(f"Error updating trade in database: {str(e)}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        total_trades = len(self.closed_trades)
        win_rate = (self.winning_trades / max(total_trades, 1)) * 100
        
        return {
            "initial_capital": self.initial_capital,
            "current_capital": self.current_capital,
            "available_capital": self.available_capital,
            "total_profit_loss": self.total_profit_loss,
            "total_profit_loss_percent": (self.total_profit_loss / self.initial_capital) * 100,
            "total_trades": total_trades,
            "open_trades": len(self.open_trades),
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "win_rate": win_rate,
            "average_profit_per_trade": self.total_profit_loss / max(total_trades, 1),
            "best_trade": max([t.actual_profit for t in self.closed_trades], default=0),
            "worst_trade": min([t.actual_profit for t in self.closed_trades], default=0),
            "current_drawdown": min(0, self.current_capital - self.initial_capital),
            "max_capital": max(self.current_capital, self.initial_capital)
        }

# Global paper trading engine instance
paper_trading_engine = PaperTradingEngine()
