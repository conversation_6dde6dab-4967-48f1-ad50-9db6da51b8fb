"""
Enhanced logging system with real-time price display and transparent calculations
"""
import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import asdict
import sys

from models.data_models import OptionsData, ManipulationSignal

class TransparentLogger:
    """
    Enhanced logger that always shows current prices, calculations, and market data
    """
    
    def __init__(self, name: str = "options_detection"):
        self.logger = logging.getLogger(name)
        self.setup_logging()
        
    def setup_logging(self):
        """Setup enhanced logging with detailed formatting"""
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatter with timestamp and detailed info
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Console handler with UTF-8 encoding
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # File handler for detailed logs with UTF-8 encoding
        file_handler = logging.FileHandler('options_detection_detailed.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.DEBUG)
    
    def log_market_data_collection(self, symbol: str, data_count: int, timestamp: datetime):
        """Log market data collection with current status"""
        self.logger.info(f"[DATA] MARKET DATA COLLECTION")
        self.logger.info(f"   Symbol: {symbol}")
        self.logger.info(f"   Data Points: {data_count}")
        self.logger.info(f"   Collection Time: {timestamp.strftime('%H:%M:%S')}")
        self.logger.info(f"   Status: {'[OK] SUCCESS' if data_count > 0 else '[WARN] NO DATA'}")
    
    def log_current_prices(self, options_data: List[OptionsData]):
        """Log current market prices transparently"""
        if not options_data:
            self.logger.warning("[WARN] NO PRICE DATA AVAILABLE")
            return

        self.logger.info("[PRICES] CURRENT MARKET PRICES")
        self.logger.info("=" * 80)
        
        # Group by symbol and show current prices
        symbols = {}
        for option in options_data:
            if option.symbol not in symbols:
                symbols[option.symbol] = {'calls': [], 'puts': []}
            
            if option.option_type.value == 'CE':
                symbols[option.symbol]['calls'].append(option)
            else:
                symbols[option.symbol]['puts'].append(option)
        
        for symbol, data in symbols.items():
            self.logger.info(f"[CHAIN] {symbol} OPTIONS CHAIN - {datetime.now().strftime('%H:%M:%S')}")
            
            # Show calls
            if data['calls']:
                self.logger.info("   CALL OPTIONS:")
                for call in sorted(data['calls'], key=lambda x: x.strike)[:5]:  # Top 5 strikes
                    self.logger.info(
                        f"     {call.strike:>7.0f} CE | "
                        f"Price: ₹{call.last_price:>6.2f} | "
                        f"Bid: ₹{call.bid_price:>6.2f} | "
                        f"Ask: ₹{call.ask_price:>6.2f} | "
                        f"Vol: {call.volume:>6,} | "
                        f"OI: {call.open_interest:>8,} | "
                        f"BidQ: {call.bid_qty:>4} | "
                        f"AskQ: {call.ask_qty:>4}"
                    )
            
            # Show puts
            if data['puts']:
                self.logger.info("   PUT OPTIONS:")
                for put in sorted(data['puts'], key=lambda x: x.strike)[:5]:  # Top 5 strikes
                    self.logger.info(
                        f"     {put.strike:>7.0f} PE | "
                        f"Price: ₹{put.last_price:>6.2f} | "
                        f"Bid: ₹{put.bid_price:>6.2f} | "
                        f"Ask: ₹{put.ask_price:>6.2f} | "
                        f"Vol: {put.volume:>6,} | "
                        f"OI: {put.open_interest:>8,} | "
                        f"BidQ: {put.bid_qty:>4} | "
                        f"AskQ: {put.ask_qty:>4}"
                    )
            
            self.logger.info("-" * 80)
    
    def log_detection_calculation(self, detector_name: str, data_points: int, 
                                calculation_details: Dict[str, Any]):
        """Log detection calculations transparently"""
        self.logger.info(f"🔍 DETECTION CALCULATION - {detector_name.upper()}")
        self.logger.info(f"   Data Points Analyzed: {data_points}")
        self.logger.info(f"   Calculation Time: {datetime.now().strftime('%H:%M:%S')}")
        
        # Log calculation details
        for key, value in calculation_details.items():
            if isinstance(value, (int, float)):
                self.logger.info(f"   {key}: {value:,.2f}")
            else:
                self.logger.info(f"   {key}: {value}")
    
    def log_manipulation_signal(self, signal: ManipulationSignal, 
                              calculation_breakdown: Dict[str, Any] = None):
        """Log manipulation signal with full transparency"""
        self.logger.warning("🚨 MANIPULATION SIGNAL DETECTED")
        self.logger.warning("=" * 80)
        self.logger.warning(f"   Pattern Type: {signal.pattern_type.value.upper()}")
        self.logger.warning(f"   Detection Time: {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.warning(f"   Confidence Level: {signal.confidence:.1%} ({signal.confidence_level.value.upper()})")
        self.logger.warning(f"   Symbols Affected: {', '.join(signal.symbols_affected)}")
        self.logger.warning(f"   Description: {signal.description}")
        
        # Financial impact
        self.logger.warning("💰 FINANCIAL IMPACT:")
        self.logger.warning(f"   Estimated Profit: ₹{signal.estimated_profit:,.2f}")
        
        # Market impact details
        self.logger.warning("📊 MARKET IMPACT:")
        for key, value in signal.market_impact.items():
            if isinstance(value, (int, float)):
                self.logger.warning(f"   {key}: {value:,.2f}")
            else:
                self.logger.warning(f"   {key}: {value}")
        
        # Calculation breakdown if provided
        if calculation_breakdown:
            self.logger.warning("🧮 CALCULATION BREAKDOWN:")
            for key, value in calculation_breakdown.items():
                if isinstance(value, (int, float)):
                    self.logger.warning(f"   {key}: {value:,.2f}")
                else:
                    self.logger.warning(f"   {key}: {value}")
        
        self.logger.warning("=" * 80)
    
    def log_price_movement(self, symbol: str, strike: float, option_type: str,
                          old_price: float, new_price: float, volume: int,
                          timestamp: datetime):
        """Log real-time price movements"""
        price_change = new_price - old_price
        price_change_pct = (price_change / old_price * 100) if old_price > 0 else 0
        
        direction = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
        
        self.logger.info(f"{direction} PRICE MOVEMENT")
        self.logger.info(f"   Option: {symbol} {strike} {option_type}")
        self.logger.info(f"   Time: {timestamp.strftime('%H:%M:%S')}")
        self.logger.info(f"   Old Price: ₹{old_price:.2f}")
        self.logger.info(f"   New Price: ₹{new_price:.2f}")
        self.logger.info(f"   Change: ₹{price_change:+.2f} ({price_change_pct:+.2f}%)")
        self.logger.info(f"   Volume: {volume:,} lots")
    
    def log_order_book_change(self, symbol: str, strike: float, option_type: str,
                            old_bid_qty: int, new_bid_qty: int,
                            old_ask_qty: int, new_ask_qty: int,
                            timestamp: datetime):
        """Log order book quantity changes"""
        bid_change = new_bid_qty - old_bid_qty
        ask_change = new_ask_qty - old_ask_qty
        
        if abs(bid_change) > 100 or abs(ask_change) > 100:  # Only log significant changes
            self.logger.info(f"📋 ORDER BOOK CHANGE")
            self.logger.info(f"   Option: {symbol} {strike} {option_type}")
            self.logger.info(f"   Time: {timestamp.strftime('%H:%M:%S')}")
            self.logger.info(f"   Bid Qty: {old_bid_qty:,} → {new_bid_qty:,} ({bid_change:+,})")
            self.logger.info(f"   Ask Qty: {old_ask_qty:,} → {new_ask_qty:,} ({ask_change:+,})")
            
            if abs(bid_change) > 1000 or abs(ask_change) > 1000:
                self.logger.warning(f"⚠️  LARGE QUANTITY CHANGE DETECTED!")
    
    def log_system_status(self, status: Dict[str, Any]):
        """Log current system status"""
        self.logger.info("🖥️  SYSTEM STATUS")
        self.logger.info(f"   Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        for component, details in status.items():
            if isinstance(details, dict):
                self.logger.info(f"   {component.upper()}:")
                for key, value in details.items():
                    self.logger.info(f"     {key}: {value}")
            else:
                self.logger.info(f"   {component}: {details}")
    
    def log_api_request(self, endpoint: str, method: str, response_time: float,
                       status_code: int, data_size: int = 0):
        """Log API requests with timing"""
        status_emoji = "✅" if status_code < 400 else "❌"
        
        self.logger.info(f"🌐 API REQUEST {status_emoji}")
        self.logger.info(f"   Endpoint: {method} {endpoint}")
        self.logger.info(f"   Status: {status_code}")
        self.logger.info(f"   Response Time: {response_time:.3f}s")
        if data_size > 0:
            self.logger.info(f"   Data Size: {data_size:,} bytes")
    
    def log_nse_api_call(self, url: str, response_time: float, status_code: int,
                        data_points: int = 0, error: str = None):
        """Log NSE API calls with detailed information"""
        status_emoji = "✅" if status_code == 200 else "❌"
        
        self.logger.info(f"🔗 NSE API CALL {status_emoji}")
        self.logger.info(f"   URL: {url}")
        self.logger.info(f"   Status: {status_code}")
        self.logger.info(f"   Response Time: {response_time:.3f}s")
        
        if status_code == 200:
            self.logger.info(f"   Data Points: {data_points}")
        else:
            self.logger.error(f"   Error: {error}")
    
    def log_detection_summary(self, cycle_number: int, total_signals: int,
                            high_confidence_signals: int, execution_time: float,
                            detectors_run: List[str]):
        """Log detection cycle summary"""
        self.logger.info("🔄 DETECTION CYCLE SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"   Cycle Number: {cycle_number}")
        self.logger.info(f"   Execution Time: {execution_time:.2f}s")
        self.logger.info(f"   Detectors Run: {len(detectors_run)}")
        self.logger.info(f"   Total Signals: {total_signals}")
        self.logger.info(f"   High Confidence: {high_confidence_signals}")
        
        if high_confidence_signals > 0:
            self.logger.warning(f"⚠️  {high_confidence_signals} HIGH CONFIDENCE SIGNALS REQUIRE ATTENTION!")
        
        self.logger.info(f"   Detectors: {', '.join(detectors_run)}")
        self.logger.info("=" * 60)
    
    def log_profit_calculation(self, signal_type: str, base_amount: float,
                             multiplier: float, final_profit: float,
                             calculation_steps: List[str]):
        """Log profit calculation steps transparently"""
        self.logger.info(f"💰 PROFIT CALCULATION - {signal_type.upper()}")
        self.logger.info(f"   Base Amount: ₹{base_amount:,.2f}")
        self.logger.info(f"   Multiplier: {multiplier:.4f}")
        
        for step in calculation_steps:
            self.logger.info(f"   Step: {step}")
        
        self.logger.info(f"   Final Estimated Profit: ₹{final_profit:,.2f}")
    
    def log_market_hours_status(self, is_market_open: bool, next_session: str = None):
        """Log market hours status"""
        status = "🟢 OPEN" if is_market_open else "🔴 CLOSED"
        self.logger.info(f"🕐 MARKET STATUS: {status}")
        
        if not is_market_open and next_session:
            self.logger.info(f"   Next Session: {next_session}")
    
    def log_error_with_context(self, error: Exception, context: Dict[str, Any]):
        """Log errors with full context"""
        self.logger.error("❌ ERROR OCCURRED")
        self.logger.error(f"   Error Type: {type(error).__name__}")
        self.logger.error(f"   Error Message: {str(error)}")
        self.logger.error(f"   Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.logger.error("   Context:")
        for key, value in context.items():
            self.logger.error(f"     {key}: {value}")

# Global enhanced logger instance
enhanced_logger = TransparentLogger()

# Convenience functions
def log_current_prices(options_data: List[OptionsData]):
    """Log current market prices"""
    enhanced_logger.log_current_prices(options_data)

def log_manipulation_signal(signal: ManipulationSignal, calculation_breakdown: Dict[str, Any] = None):
    """Log manipulation signal"""
    enhanced_logger.log_manipulation_signal(signal, calculation_breakdown)

def log_price_movement(symbol: str, strike: float, option_type: str,
                      old_price: float, new_price: float, volume: int):
    """Log price movement"""
    enhanced_logger.log_price_movement(symbol, strike, option_type, old_price, new_price, volume, datetime.now())

def log_detection_summary(cycle_number: int, total_signals: int, high_confidence_signals: int,
                         execution_time: float, detectors_run: List[str]):
    """Log detection cycle summary"""
    enhanced_logger.log_detection_summary(cycle_number, total_signals, high_confidence_signals, execution_time, detectors_run)
