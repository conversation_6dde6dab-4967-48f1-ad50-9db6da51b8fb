"""
NSE Data Collector with async support and rate limiting
"""
import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import logging
from asyncio_throttle import Throttler
import json

from config.settings import settings, NSE_URLS
from models.data_models import OptionsData, MarketData, OptionType
from utils.cache import CacheManager
from utils.exceptions import DataCollectionError, RateLimitError
from utils.enhanced_logging import enhanced_logger
from data_collectors.mock_data_generator import mock_data_generator

logger = logging.getLogger(__name__)

class NSEDataCollector:
    """
    Async NSE data collector with rate limiting and caching
    """
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.throttler = Throttler(
            rate_limit=settings.nse_api.requests_per_second,
            period=1.0
        )
        self.cache = CacheManager()
        self.headers = self._get_enhanced_headers()
        self.cookies = {}
        self.session_initialized = False
        
        # Track request statistics
        self.request_count = 0
        self.error_count = 0
        self.last_request_time = None

    def _get_enhanced_headers(self) -> Dict[str, str]:
        """Get enhanced headers for NSE API requests"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': 'https://www.nseindia.com/',
            'Origin': 'https://www.nseindia.com'
        }
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close_session()
    
    async def start_session(self):
        """Initialize aiohttp session with proper authentication"""
        connector = aiohttp.TCPConnector(
            limit=20,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True
        )

        timeout = aiohttp.ClientTimeout(total=30, connect=10)

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self.headers
        )

        # Initialize session with NSE homepage to get cookies
        await self._initialize_nse_session()

        logger.info("NSE data collector session started")

    async def _initialize_nse_session(self):
        """Initialize NSE session by visiting homepage to get cookies"""
        try:
            # Visit NSE homepage to establish session
            async with self.session.get('https://www.nseindia.com') as response:
                if response.status == 200:
                    # Store cookies for future requests
                    for cookie in response.cookies:
                        self.cookies[cookie.key] = cookie.value
                    self.session_initialized = True
                    logger.info("NSE session initialized successfully")
                else:
                    logger.warning(f"NSE homepage returned status {response.status}")
        except Exception as e:
            logger.error(f"Failed to initialize NSE session: {str(e)}")

    async def close_session(self):
        """Close aiohttp session"""
        if self.session:
            await self.session.close()
            logger.info("NSE data collector session closed")
    
    async def _make_request(self, url: str, params: Dict = None) -> Dict[str, Any]:
        """
        Make rate-limited request to NSE API
        """
        if not self.session:
            raise DataCollectionError("Session not initialized")

        start_time = time.time()

        # Apply rate limiting
        async with self.throttler:
            try:
                # Check cache first
                cache_key = f"nse_request:{url}:{str(params)}"
                cached_data = await self.cache.get(cache_key)
                if cached_data:
                    enhanced_logger.logger.debug(f"📋 Cache hit for {url}")
                    return cached_data

                self.request_count += 1
                self.last_request_time = datetime.now()

                logger.info(f"Making NSE API request to {url}")
                if params:
                    logger.info(f"   Parameters: {params}")

                # Prepare request with enhanced headers and cookies
                request_headers = self.headers.copy()
                if self.cookies:
                    request_headers['Cookie'] = '; '.join([f"{k}={v}" for k, v in self.cookies.items()])

                async with self.session.get(url, params=params, headers=request_headers) as response:
                    response_time = time.time() - start_time

                    if response.status == 200:
                        data = await response.json()
                        data_size = len(str(data))

                        # Log successful API call
                        logger.info(f"NSE API success: {response.status} in {response_time:.3f}s")
                        if 'records' in data:
                            data_points = len(data.get('records', {}).get('data', []))
                            logger.info(f"Retrieved {data_points} data points")

                        # Cache successful responses for 30 seconds
                        await self.cache.set(cache_key, data, ttl=30)

                        return data
                    elif response.status == 429:
                        self.error_count += 1
                        logger.warning(f"NSE API rate limited: {response.status} in {response_time:.3f}s")
                        raise RateLimitError(f"Rate limit exceeded: {response.status}")
                    else:
                        self.error_count += 1
                        error_text = await response.text()
                        logger.error(f"NSE API error: {response.status} in {response_time:.3f}s - {error_text[:100]}")
                        raise DataCollectionError(
                            f"HTTP {response.status}: {error_text}"
                        )
                        
            except asyncio.TimeoutError:
                self.error_count += 1
                raise DataCollectionError(f"Timeout requesting {url}")
            except aiohttp.ClientError as e:
                self.error_count += 1
                raise DataCollectionError(f"Client error: {str(e)}")
    
    async def get_options_chain(self, symbol: str) -> List[OptionsData]:
        """
        Get options chain data for a symbol
        """
        try:
            url = NSE_URLS["options"]
            params = {"symbol": symbol}
            
            data = await self._make_request(url, params)
            
            if not data or "records" not in data:
                raise DataCollectionError(f"Invalid options data for {symbol}")
            
            options_list = []
            records = data["records"]["data"]
            
            for record in records:
                expiry_date = datetime.strptime(record["expiryDate"], "%d-%b-%Y")
                strike = float(record["strikePrice"])
                
                # Process Call options
                if "CE" in record:
                    ce_data = record["CE"]
                    options_list.append(OptionsData(
                        symbol=symbol,
                        expiry_date=expiry_date,
                        strike=strike,
                        option_type=OptionType.CALL,
                        last_price=float(ce_data.get("lastPrice", 0)),
                        bid_price=float(ce_data.get("bidprice", 0)),
                        ask_price=float(ce_data.get("askPrice", 0)),
                        volume=int(ce_data.get("totalTradedVolume", 0)),
                        open_interest=int(ce_data.get("openInterest", 0)),
                        bid_qty=int(ce_data.get("bidQty", 0)),
                        ask_qty=int(ce_data.get("askQty", 0)),
                        delta=float(ce_data.get("delta", 0)) if ce_data.get("delta") else None,
                        gamma=float(ce_data.get("gamma", 0)) if ce_data.get("gamma") else None,
                        theta=float(ce_data.get("theta", 0)) if ce_data.get("theta") else None,
                        vega=float(ce_data.get("vega", 0)) if ce_data.get("vega") else None,
                        implied_volatility=float(ce_data.get("impliedVolatility", 0)) if ce_data.get("impliedVolatility") else None,
                        timestamp=datetime.now(),
                        change=float(ce_data.get("change", 0)),
                        percent_change=float(ce_data.get("pChange", 0))
                    ))
                
                # Process Put options
                if "PE" in record:
                    pe_data = record["PE"]
                    options_list.append(OptionsData(
                        symbol=symbol,
                        expiry_date=expiry_date,
                        strike=strike,
                        option_type=OptionType.PUT,
                        last_price=float(pe_data.get("lastPrice", 0)),
                        bid_price=float(pe_data.get("bidprice", 0)),
                        ask_price=float(pe_data.get("askPrice", 0)),
                        volume=int(pe_data.get("totalTradedVolume", 0)),
                        open_interest=int(pe_data.get("openInterest", 0)),
                        bid_qty=int(pe_data.get("bidQty", 0)),
                        ask_qty=int(pe_data.get("askQty", 0)),
                        delta=float(pe_data.get("delta", 0)) if pe_data.get("delta") else None,
                        gamma=float(pe_data.get("gamma", 0)) if pe_data.get("gamma") else None,
                        theta=float(pe_data.get("theta", 0)) if pe_data.get("theta") else None,
                        vega=float(pe_data.get("vega", 0)) if pe_data.get("vega") else None,
                        implied_volatility=float(pe_data.get("impliedVolatility", 0)) if pe_data.get("impliedVolatility") else None,
                        timestamp=datetime.now(),
                        change=float(pe_data.get("change", 0)),
                        percent_change=float(pe_data.get("pChange", 0))
                    ))
            
            # Log collected data with current prices
            logger.info(f"Collected {len(options_list)} options data points for {symbol}")

            if options_list:
                # Show sample of current prices
                logger.info(f"SAMPLE CURRENT PRICES for {symbol}:")
                for option in options_list[:3]:  # Show first 3 options
                    logger.info(
                        f"   {option.strike} {option.option_type.value}: "
                        f"Rs{option.last_price:.2f} | Vol: {option.volume:,} | OI: {option.open_interest:,}"
                    )

            return options_list
            
        except Exception as e:
            logger.error(f"Error collecting options data for {symbol}: {str(e)}")

            # Fallback to mock data for demonstration purposes
            logger.warning(f"Falling back to mock data for {symbol}")
            try:
                mock_options = mock_data_generator.generate_options_chain(symbol)
                logger.info(f"Generated {len(mock_options)} mock options for {symbol}")
                return mock_options
            except Exception as mock_error:
                logger.error(f"Failed to generate mock data: {str(mock_error)}")
                raise DataCollectionError(f"Failed to collect options data: {str(e)}")
    
    async def get_market_status(self) -> Dict[str, Any]:
        """
        Get current market status
        """
        try:
            url = NSE_URLS["market_status"]
            data = await self._make_request(url)
            
            return {
                "market_status": data.get("marketState", []),
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error getting market status: {str(e)}")
            raise DataCollectionError(f"Failed to get market status: {str(e)}")
    
    async def get_underlying_data(self, symbol: str) -> Dict[str, Any]:
        """
        Get underlying index data
        """
        try:
            # This would typically come from the options chain data
            url = NSE_URLS["options"]
            params = {"symbol": symbol}
            
            data = await self._make_request(url, params)
            
            if "records" in data and "underlyingValue" in data["records"]:
                underlying_value = data["records"]["underlyingValue"]
                return {
                    "symbol": symbol,
                    "price": float(underlying_value),
                    "timestamp": datetime.now()
                }
            
            raise DataCollectionError(f"No underlying data found for {symbol}")
            
        except Exception as e:
            logger.error(f"Error getting underlying data for {symbol}: {str(e)}")
            raise DataCollectionError(f"Failed to get underlying data: {str(e)}")
    
    async def collect_all_symbols(self, symbols: List[str]) -> Dict[str, List[OptionsData]]:
        """
        Collect options data for all symbols concurrently
        """
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(
                self.get_options_chain(symbol),
                name=f"collect_{symbol}"
            )
            tasks.append((symbol, task))
        
        results = {}
        for symbol, task in tasks:
            try:
                results[symbol] = await task
            except Exception as e:
                logger.error(f"Failed to collect data for {symbol}: {str(e)}")
                results[symbol] = []
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get collector statistics
        """
        return {
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "error_rate": self.error_count / max(self.request_count, 1),
            "last_request_time": self.last_request_time,
            "session_active": self.session is not None and not self.session.closed
        }
